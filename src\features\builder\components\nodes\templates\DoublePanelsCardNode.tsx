import React, { useState, useCallback, useRef } from "react";
import { NodeRendererProps } from "../NodeFactory";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { Button } from "@/components/ui/button";
import { Type, Image as ImageIcon, GripVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PanelContentType {
  type: "text" | "image";
  content: string;
  title?: string;
}

const DoublePanelsCardNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({ 
  node,
  ...htmlProps
}) => {
  // Get panel data from node properties
  const leftPanel: PanelContentType = {
    type: (node.properties?.leftPanelType as string) || "text",
    content: (node.properties?.leftPanelContent as string) || "",
    title: (node.properties?.leftPanelTitle as string) || "",
  };

  const rightPanel: PanelContentType = {
    type: (node.properties?.rightPanelType as string) || "text", 
    content: (node.properties?.rightPanelContent as string) || "",
    title: (node.properties?.rightPanelTitle as string) || "",
  };

  const leftWidth = (node.properties?.leftWidth as number) || 50;
  const rightWidth = 100 - leftWidth;

  const [isDragging, setIsDragging] = useState(false);
  const [showLeftDropdown, setShowLeftDropdown] = useState(false);
  const [showRightDropdown, setShowRightDropdown] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      
      const rect = containerRef.current.getBoundingClientRect();
      const newLeftWidth = Math.max(10, Math.min(90, ((e.clientX - rect.left) / rect.width) * 100));
      
      // Update node properties through parent component
      // This would need to be connected to the builder context
      console.log('New left width:', newLeftWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, []);

  const renderPanelContent = (panel: PanelContentType, side: 'left' | 'right') => {
    const showDropdown = side === 'left' ? showLeftDropdown : showRightDropdown;
    const setShowDropdown = side === 'left' ? setShowLeftDropdown : setShowRightDropdown;

    return (
      <div className="relative h-full group">
        {/* Content Type Selector */}
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu open={showDropdown} onOpenChange={setShowDropdown}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0 bg-white/90 backdrop-blur-sm">
                {panel.type === 'text' ? <Type className="h-4 w-4" /> : <ImageIcon className="h-4 w-4" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => console.log(`Set ${side} to text`)}>
                <Type className="mr-2 h-4 w-4" />
                Text
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => console.log(`Set ${side} to image`)}>
                <ImageIcon className="mr-2 h-4 w-4" />
                Image
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Panel Content */}
        <div className="h-full">
          {panel.type === "image" ? (
            <ImageLoader
              src={panel.content}
              alt={panel.title}
              className="w-full h-full object-cover rounded-md"
              fallbackText={panel.title || "Click to add image"}
            />
          ) : (
            <div className="h-full p-6 flex items-center justify-center">
              <div className="text-center space-y-4">
                {panel.title && (
                  <h1 className="text-[64px] font-medium whitespace-nowrap text-[#0177BD] text-center">
                    {panel.title}
                  </h1>
                )}
                <div 
                  className="text-gray-700 text-sm leading-relaxed whitespace-pre-line"
                  dangerouslySetInnerHTML={{ __html: panel.content || "Click to add text" }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "overflow-hidden transition-all duration-300 bg-transparent min-h-64 h-max relative",
        node.style?.className
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      <div className="flex h-full items-center relative">
        {/* Left Panel */}
        <div 
          className="relative h-full"
          style={{ width: `${leftWidth}%` }}
        >
          {renderPanelContent(leftPanel, 'left')}
        </div>

        {/* Resize Divider */}
        <div 
          className={cn(
            "w-1 h-full bg-gray-300 cursor-col-resize hover:bg-blue-500 transition-colors relative group",
            isDragging && "bg-blue-500"
          )}
          onMouseDown={handleMouseDown}
        >
          <div className="absolute inset-y-0 -left-1 -right-1 flex items-center justify-center">
            <GripVertical className="h-4 w-4 text-gray-500 group-hover:text-blue-500 transition-colors" />
          </div>
        </div>

        {/* Right Panel */}
        <div 
          className="relative h-full"
          style={{ width: `${rightWidth}%` }}
        >
          {renderPanelContent(rightPanel, 'right')}
        </div>
      </div>
    </div>
  );
};

// Memoized template node to prevent re-renders
export const DoublePanelsCardNode = React.memo(
  DoublePanelsCardNodeComponent,
  compareNodeWithProperties
);
